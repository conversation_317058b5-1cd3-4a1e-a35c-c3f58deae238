{"name": "gzznhz", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@mdi/js": "^7.4.47", "@microsoft/fetch-event-source": "^2.0.1", "@vueuse/core": "^13.2.0", "@zumer/snapdom": "^1.2.5", "axios": "^1.9.0", "clipboard": "^2.0.11", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.10.4", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0", "markdown-it-katex": "^2.0.3", "pinia": "^3.0.1", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-router": "4"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "path": "^0.12.7", "sass": "^1.85.0", "sass-loader": "^16.0.5", "vite": "^6.1.0"}}